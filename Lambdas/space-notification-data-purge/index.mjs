import axios from 'axios';

const CONFIG = {
  MONGO: {
    BASE_URL: process.env.MONGO_ATLAS_BASE_URL,
    API_KEY: process.env.MONGO_ATLAS_API_KEY,
    APP: process.env.MONGO_APP,
    DATABASE: process.env.MONGO_DATABASE,
    DATASOURCE: process.env.MONGO_DATASOURCE,
    COLLECTION: 'notifications',
  }
};

export const handler = async (event) => {
  try {
    // Calculate the threshold for expired messages (45 days ago)
    const currentTime = Math.floor(Date.now() / 1000); // Get current UNIX timestamp in seconds
    const thresholdTime = currentTime - (45 * 24 * 3600); // Threshold time for 45 days before current time
  
    const endpointUrl = `${CONFIG.MONGO.BASE_URL}/app/${CONFIG.MONGO.APP}/endpoint/data/v1/action/updateMany`;

  
    const query = {
      filter: {
        'messages.expiresAt': { $lt: thresholdTime }
      },
      update: {
        $pull: { messages: { expiresAt: { $lt: thresholdTime } } }
      },
      database: CONFIG.MONGO.DATABASE,
      collection: CONFIG.MONGO.COLLECTION,
      dataSource: CONFIG.MONGO.DATASOURCE,

    };

    // Axios API call to MongoDB Atlas Data API
    const response = await axios.post(
      endpointUrl,
      query,
      {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Request-Headers": "*",
          "api-key": CONFIG.MONGO.API_KEY
        }
      }
    );

    console.log(`Update successful! Modified ${response.data.modifiedCount} documents.`);
    
    return {
      statusCode: 200,
      body: JSON.stringify(`Updated ${response.data.modifiedCount} documents.`),
    };
    
  } catch (error) {
    console.error('Error:', error);

    return {
      statusCode: 500,
      body: JSON.stringify('Error deleting expired messages'),
    };
  }
};

