const nodemailer = require('nodemailer');
const fs = require('fs').promises; // Using promises version for file operations
const axios = require('axios');
const nodemailerMock = require('nodemailer-mock');
const AWS = require('aws-sdk');

const { fetchMockData } = require('./mockData.js');
const isLocalDev = process.env.LOCAL_DEV === "true" || false;

// Validator for ID
function isValidId(id) {
    return Number.isInteger(id) && id > 0;
}

// Validator for brand
function isValidBrand(brand) {
    return typeof brand === 'string' && brand.trim() !== '';
}

// Validator for country
function isValidCountry(country) {
    return typeof country === 'string' && country.trim() !== '';
}

// Function to fetch data from the API
async function fetchData(id, brand, country) {
    try {
        if (isLocalDev) {
            return await fetchMockData();
        } else {
            const url = `${process.env.LOG_INCIDENT_END_POINT}${id}`;
            const params = { id, brand, country };
            const response = await axios.get(url, { params });
            return response.data;
        }
    } catch (error) {
        throw new Error(`Error fetching data: ${error.message}`);
    }
}

// Function to read HTML template file
async function readHtmlTemplate(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return data;
    } catch (error) {
        throw new Error(`Error reading HTML template: ${error.message}`);
    }
}

// Function to create email content
function createEmailContent(htmlTemplate, data) {
    const placeholders = ['{{email_title}}', '{{title}}', '{{VIN}}', '{{customerEmail}}', '{{customerEmailLink}}', '{{description}}', '{{backendId}}'];
    const values = [data.title, data.title, data.vin, data.email, data.email, data.comment, data.incidentid];

    placeholders.forEach((placeholder, index) => {
        htmlTemplate = htmlTemplate.replace(new RegExp(placeholder, 'g'), values[index]);
    });

    return htmlTemplate;
}

// Function to fetch file contents from URLs and filter only images
async function fetchAttachments(data) {
    const attachmentPromises = data.files.filter(isImage).map(async (fileUrl) => {
        const response = await axios.get(fileUrl, { responseType: 'arraybuffer' });
        return {
            filename: fileUrl.split('/').pop(),
            content: response.data
        };
    });
    return Promise.all(attachmentPromises);
}

// Function to send email with attachments and content
async function sendEmail(data, htmlContent, attachments) {
    try {
        const transporter = isLocalDev ? nodemailerMock.createTransport({ transport: nodemailerMock.mock }) : nodemailer.createTransport({ SES: new AWS.SES({}) });
        const mailOptions = {
            from: process.env.SENDER_EMAIL,
            to: process.env.RECIPIENT_EMAIL,
            subject: data.title,
            html: htmlContent,
            attachments
        };
        const info = await transporter.sendMail(mailOptions);
        console.log('Email sent:', info.response);
        if (isLocalDev) {
            console.log('Email content:', htmlContent);
        }
    } catch (error) {
        throw new Error(`Error sending email: ${error.message}`);
    }
}

// Function to check if a file URL is an image
function isImage(fileUrl) {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif', 'webp', 'svg', 'ico'];
    const fileExtension = fileUrl.split('.').pop().toLowerCase();
    return imageExtensions.includes(fileExtension);
}

// Main function
async function main(id, brand, country) {
    try {
        // Proceed with fetching and processing data
        const data = await fetchData(id, brand, country);
        if (!data.success) {
            throw new Error("Data response format invalid");
        }
        const apiData = data.success;
        const htmlTemplate = await readHtmlTemplate('email_template.html');
        const htmlContent = createEmailContent(htmlTemplate, apiData);
        const attachments = await fetchAttachments(apiData);

        await sendEmail(apiData, htmlContent, attachments);
    } catch (error) {
        console.error('An error occurred:', error);
    }
}

// Test the main function in a local environment
if (isLocalDev) {
    main(177, 'AC', 'FR').catch(error => console.error(error));
}

// Lambda handler
exports.handler = async function(event) {
    try {
        for (const record of event.Records) {
            console.log('Received message record var:', record.body);
            const message = record.body;
            const { id, brand, country } = JSON.parse(message);
            await main(id, brand, country);
        }
        return { statusCode: 200, body: JSON.stringify('Email sent successfully!') };
    } catch (error) {
        console.error('An error occurred:', error);
        return { statusCode: 500, body: JSON.stringify({ error: 'An error occurred' }) };
    }
};
