// Mock data function
async function fetchMockData() {
    return {
        "success": {
            "incidentid": 1147,
            "email": "<EMAIL>",
            "vin": "VR7ACYHZJJL062583",
            "id_client": "ACNT200000279079",
            "title": "test",
            "comment": "Test",
            "mail_send_status": false,
            "mail_nb_reminders": 0,
            "creation_date": "2023-07-07T08:44:48+00:00",
            "status": 0,
            "site_code": "AC_FR_ESP",
            "culture": "fr-FR",
            "optin1": false,
            "optin2": true,
            "files": [
                "https://media-integ.mym.awsmpsa.com/logincidents/1147/AND-1_9_1-2023-07-07-obit_citroen_01_yopmail.com.zip",
                "https://media-integ.mym.awsmpsa.com/logincidents/1147/Screenshot_20230703-113909_Space.jpg",
                "https://media-integ.mym.awsmpsa.com/logincidents/1147/file-sample_150kB.pdf",
                "https://media-integ.mym.awsmpsa.com/logincidents/1147/sample-zip-file.zip",
                "https://media-integ.mym.awsmpsa.com/logincidents/1147/sample2.txt"
            ],
            "type": "non-care",
            "has_log_file": true
        }
    };
}

module.exports = { fetchMockData };
