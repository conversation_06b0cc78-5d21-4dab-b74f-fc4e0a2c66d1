#!/bin/bash

branch_name=$1
targetEnvironment=$2
currentDate=$3
lambdaName=$4

if [ "$lambdaName" = "lambda-authorizer" ]; then
  FUNCTION_NAME="space-$targetEnvironment-${lambdaName}"
elif [ "$lambdaName" = "lambda-mopCognitoToken" ]; then
  FUNCTION_NAME="mopCognitoToken-$targetEnvironment"
elif [ "$lambdaName" = "lambda-logincident" ]; then
  FUNCTION_NAME="space-logincident-$targetEnvironment"
elif [ "$lambdaName" = "lambda-mongodb-trigger" ]; then
  FUNCTION_NAME="space-mongodb-trigger"
elif [ "$lambdaName" = "space-arbitration-data-extraction-emea" ]; then
  FUNCTION_NAME="space-arbitration-data-extraction-emea-$targetEnvironment"
elif [ "$lambdaName" = "space-arbitration-data-extraction-na" ]; then
  FUNCTION_NAME="space-arbitration-data-extraction-na-$targetEnvironment"
elif [ "$lambdaName" = "space-app-phyd-scores" ]; then
  FUNCTION_NAME="space-app-phyd-scores"
elif [ "$lambdaName" = "space-app-phyd-contracts" ]; then
  FUNCTION_NAME="space-app-phyd-contracts"  
elif [ "$lambdaName" = "space-notification-data-purge" ]; then
  FUNCTION_NAME="space-notification-data-purge" 
fi

if [ "$lambdaName" = "space-arbitration-data-extraction-na" ] && [ "$targetEnvironment" = "prod" ]; then
  FUNCTION_NAME="space-arbitration-data-extraction-na"
elif [ "$lambdaName" = "space-arbitration-data-extraction-emea" ] && [ "$targetEnvironment" = "prod" ]; then
  FUNCTION_NAME="space-arbitration-data-extraction-emea"
fi

if [ "$lambdaName" = "lambda-logincident" ] && [ "$targetEnvironment" = "prod" ]; then
  FUNCTION_NAME="space-logincident"
elif [ "$lambdaName" = "lambda-mopCognitoToken" ] && [ "$targetEnvironment" = "prod" ]; then
  FUNCTION_NAME="mopCognitoToken"
fi
 # Install the script  dependencies packages
echo '-------------------Install the script  dependencies packages -------------------'
cd $lambdaName

npm install

 #create deploy package zip
echo "-------------------create deploy package zip -------------------------"
zip -r  lambda.zip .


 #deploy  package to lambda
echo "-------------------deploy  package to $FUNCTION_NAME lambda -------------------------"
aws lambda update-function-code --region eu-west-1 --function-name $FUNCTION_NAME  --zip-file fileb://lambda.zip
