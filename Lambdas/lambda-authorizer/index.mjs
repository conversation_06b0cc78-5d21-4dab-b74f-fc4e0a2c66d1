import checkCognitoToken from "./CognitoAuthorizer.mjs"
import checkCVSToken from "./CVSAuthorizer.mjs"

export const handler = async(event, context, callback) => {
  const resource = process.env.RESOURCE;
  console.log(resource);
  const userPools = [process.env.POOL_ID, process.env.MASERATI_POOL_ID, process.env.NAFTA_MASERATI_POOL_ID, process.env.NAFTA_POOL_ID];

  const cognitoBaseUrl = "https://cognito-identity.amazonaws.com";
  const token = event.headers.token
  console.log("the input token "+token)
  const tokenType = event.headers.type 
  const brand = event.queryStringParameters?.brand || "STELLA";
  const source = event.queryStringParameters?.source || "SPACEWEB";
  const country = event.queryStringParameters?.country || "";
  const os = event.queryStringParameters?.os || "";
  console.log("the input tokenType "+tokenType)
  console.log("the input brand "+brand)
  var userId = null;
  
  if (tokenType == "CVS") {
    console.log("resolving token cvs ")
    if (!brand) {
        console.log("brand is empty for cvs "+brand)
    }
    userId = await checkCVSToken(token, brand,country,source,os,tokenType)
  } else if (tokenType == "COGNITO") {
    console.log("resolving token "+tokenType)
    userId = await checkCognitoToken(token, userPools, cognitoBaseUrl,tokenType, brand, country, source, os)
  } 
  if (userId) {
    return callback(null, generateAllow(userId, resource));
  }
  return callback("Unauthorized")
}

  /*
   *function to generate a policy
   * effect: Allow/Deny
   * resource: the resource to allow/deny the access for
   * princilId : identifier of the policy (we put userId in this case)
   */
  var generatePolicy = function(principalId, effect, resource) {
      // Required output:
      var authResponse = {};
      authResponse.principalId = principalId;
      if (effect && resource) {
          var policyDocument = {};
          policyDocument.Statement = [];
          var statementOne = {};
          statementOne.Action = 'execute-api:Invoke'; // default action
          statementOne.Effect = effect;
          statementOne.Resource = resource;
          policyDocument.Statement[0] = statementOne;
          authResponse.policyDocument = policyDocument;
      }
      // Optional output with custom properties of the user.
      authResponse.context = {
          "userId": principalId,
      };
      return authResponse;
  }
 
  var generateAllow = function(principalId, resource) {
      return generatePolicy(principalId, 'Allow', resource);
      
  }
