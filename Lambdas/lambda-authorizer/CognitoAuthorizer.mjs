import { JwtRsaVerifier } from "aws-jwt-verify";

   /*
   * as we have multiple pools per environnement 
   * loop over the list of pools to verify the token
   * return Allow Policy for the first pool that validate the token
   */
 export default async function checkCognitoToken(token, userPools, cognitoBaseUrl, tokenType, brand, country, source, os) {  
  for (var userPoolId of userPools) {
    try {
      const payload = await getVerifier(userPoolId, cognitoBaseUrl).verify(token);
      console.log("Token is valid. Payload:", payload);
      console.log(`Token valid in userPool ${userPoolId} !`);
      const userId = getUserId(payload);
      var options = { timeout: 10000, headers: {token}, query: {brand}};
      const url = process.env.SP_PROC_ME_URL+"/v1/introspection?brand="+brand+"&country="+country+"&source="+source+"&os="+os+"&tokenType="+tokenType;
      let res = await fetch(url, options)
      console.log("calling "+url+" results: "+res)
      return userId;
    } catch (error) {
        console.log(`Token not valid in userPool ${userPoolId} !`, error);   
    }
  }
}
  /*
  * function to constract the JwtRsaVerifier Object
  */
  function getVerifier(audience, cognitoBaseUrl) {
    return JwtRsaVerifier.create({
      issuer: cognitoBaseUrl,
      audience: audience, 
      jwksUri: `${cognitoBaseUrl}/.well-known/jwks_uri`,
    });
  }
  
  /*
  * function to get the userId from the paiload
  * in this example the userId is "032ff177af82467ea6ddfaecffe33ca1" :
  * "amr": [
  * "authenticated",
  * "auth.cognito.custom.gigya.intg",
  * "auth.cognito.custom.gigya.intg:eu-west-1:bc2a548d-18a8-4755-bfe7-8f69fd27b2c9:032ff177af82467ea6ddfaecffe33ca1"
  *]
  */
  function getUserId(payload) {
    var amr = payload['amr'][2].split(':');
    var userId = amr[3];
    return userId;
  }
