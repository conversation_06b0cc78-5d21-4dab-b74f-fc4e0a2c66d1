{"name": "driving-score-processor", "version": "1.0.0", "description": "Lambda function to process driving scores and update MongoDB", "main": "processDrivingScore.mjs", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lambda", "mongodb", "driving-score"], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.485.0", "axios": "^1.6.2", "csv-parse": "^5.5.3"}}