import axios from 'axios';
import { parse } from 'csv-parse';
import { S3 } from '@aws-sdk/client-s3';

const s3 = new S3();

const CONFIG = {
    MONGO: {
        BASE_URL: process.env.MONGO_ATLAS_BASE_URL,
        API_KEY: process.env.MONGO_ATLAS_API_KEY,
        APP: process.env.MONGO_APP,
        DATABASE: process.env.MONGO_DATABASE,
        DATASOURCE: process.env.MONGO_DATASOURCE,
        COLLECTION: 'drivingScore',
    },
};

const REQUIRED_COLUMNS = [
    'VIN',
    'Date',
    'Overall_driving_score',
    'Avg_daily_score',
    'Dynamics_high',
    'Dynamics_med',
    'Dynamics_low',
    'Deceleration_high',
    'Deceleration_med',
    'Deceleration_low',
    'Cornering_high',
    'Cornering_med',
    'Cornering_low'
];

const validateColumns = (headers) => {
    const headerNames = headers.map(header => header.name);
    const missingColumns = REQUIRED_COLUMNS.filter(col => !headerNames.includes(col));

    if (missingColumns.length > 0) {
        console.error('Column validation failed');
        missingColumns.forEach(column => {
            console.error(`Missing column: ${column}`);
        });
        throw new Error('Required columns are missing in the CSV file');
    }
    return true;
};

const convertToFloat = (value) => {
    if (value === null || value === undefined || value === '') {
        return null;
    }
    const floatValue = parseFloat(value);
    return isNaN(floatValue) ? null : floatValue;
};

const processRowData = (row) => {
    const vin = row.VIN?.trim();
    if (!vin || vin.length !== 17) {
        return null;
    }

    const scores = {
        overallScore: convertToFloat(row.Overall_driving_score),
        dailyScore: convertToFloat(row.Avg_daily_score),
        dynamicsHigh: convertToFloat(row.Dynamics_high),
        dynamicsMed: convertToFloat(row.Dynamics_med),
        dynamicsLow: convertToFloat(row.Dynamics_low),
        decelerationHigh: convertToFloat(row.Deceleration_high),
        decelerationMed: convertToFloat(row.Deceleration_med),
        decelerationLow: convertToFloat(row.Deceleration_low),
        corneringHigh: convertToFloat(row.Cornering_high),
        corneringMed: convertToFloat(row.Cornering_med),
        corneringLow: convertToFloat(row.Cornering_low)
    };

    const missingScores = Object.entries(scores)
        .filter(([_, value]) => value === null)
        .map(([key, _]) => key);

    if (missingScores.length > 0) {
        console.log(`VIN ${row.VIN}: Skipped`);
        return null;
    }

    return {
        vin: row.VIN,
        updateDate: row.Date || null,
        overallScore: {
            value: scores.overallScore
        },
        dailyScore: {
            value: scores.dailyScore,
            subScore: {
                dynamincs: {
                    percentageOfGood: scores.dynamicsHigh,
                    percentageOfAverage: scores.dynamicsMed,
                    percentageOfBad: scores.dynamicsLow,
                    tips: ""
                },
                decelaration: {
                    percentageOfGood: scores.decelerationHigh,
                    percentageOfAverage: scores.decelerationMed,
                    percentageOfBad: scores.decelerationLow,
                    tips: ""
                },
                cornering: {
                    percentageOfGood: scores.corneringHigh,
                    percentageOfAverage: scores.corneringMed,
                    percentageOfBad: scores.corneringLow,
                    tips: ""
                }
            }
        }
    };
};

const updateDocument = async (vin, document) => {
    const url = `${CONFIG.MONGO.BASE_URL}/app/${CONFIG.MONGO.APP}/endpoint/data/v1/action/updateOne`;
    try {
        const response = await axios.post(url, {
            collection: CONFIG.MONGO.COLLECTION,
            database: CONFIG.MONGO.DATABASE,
            dataSource: CONFIG.MONGO.DATASOURCE,
            filter: { vin: vin },
            update: { $set: document }
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Request-Headers': '*',
                'api-key': CONFIG.MONGO.API_KEY
            }
        });

        if (response.status !== 200 && response.status !== 201) {
            throw new Error(`MongoDB update failed with status ${response.status}: ${JSON.stringify(response.data)}`);
        }
        return response;
    } catch (error) {
        console.error(`Error updating document for VIN ${vin}:`, error.message);
        throw error;
    }
};

const insertDocument = async (document) => {
    const url = `${CONFIG.MONGO.BASE_URL}/app/${CONFIG.MONGO.APP}/endpoint/data/v1/action/insertOne`;
    try {
        const response = await axios.post(url, {
            collection: CONFIG.MONGO.COLLECTION,
            database: CONFIG.MONGO.DATABASE,
            dataSource: CONFIG.MONGO.DATASOURCE,
            document
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Request-Headers': '*',
                'api-key': CONFIG.MONGO.API_KEY
            }
        });

        if (response.status !== 200 && response.status !== 201) {
            throw new Error(`MongoDB insert failed with status ${response.status}: ${JSON.stringify(response.data)}`);
        }
        return response;
    } catch (error) {
        console.error(`Error inserting document for VIN ${document.vin}:`, error.message);
        throw error;
    }
};

const processCSVRow = async (row) => {
    try {
        const processedData = processRowData(row);
        if (processedData === null) {
            console.log(`Skipping row: Invalid data for VIN ${row.VIN}`);
            return { status: 'skipped', vin: row.VIN };
        }

        const updateResponse = await updateDocument(processedData.vin, processedData);
        if (updateResponse.data.matchedCount === 0) {
            await insertDocument(processedData);
            console.log(`Created new document for VIN ${processedData.vin}`);
            return { status: 'created', vin: processedData.vin };
        }

        console.log(`Updated existing document for VIN ${processedData.vin}`);
        return { status: 'updated', vin: processedData.vin };
    } catch (error) {
        console.error(`Error processing document for VIN ${row.VIN}:`, error);
        return { status: 'error', vin: row.VIN, error };
    }
};

const parseCSV = async (fileContent) => {
    const records = [];
    let headers = null;

    const parser = parse({
        columns: (headers) => {
            // Convert headers to objects with name property
            return headers.map(h => ({ name: h.trim() }));
        },
        skip_empty_lines: true,
        on_record: (record, context) => {
            if (!headers) {
                headers = context.columns;
                try {
                    validateColumns(headers);
                } catch (error) {
                    throw error;
                }
            }
            return record;
        }
    });

    return new Promise((resolve, reject) => {
        parser.on('readable', () => {
            let record;
            while ((record = parser.read()) !== null) {
                records.push(record);
            }
        });

        parser.on('error', (error) => {
            console.error('Error parsing CSV:', error.message);
            reject(error);
        });

        parser.on('end', () => resolve(records));

        const content = Buffer.isBuffer(fileContent) ? fileContent.toString() : fileContent;
        parser.write(content);
        parser.end();
    });
};

export const handler = async (event) => {
    try {
        const bucketName = event.Records[0].s3.bucket.name;
        const fileName = decodeURIComponent(event.Records[0].s3.object.key.replace(/\+/g, ' '));

        if (!fileName.toLowerCase().endsWith('.csv')) {
            const error = new Error(`Invalid file format. Only CSV files are supported. Received: ${fileName}`);
            console.error(error.message);
            throw error;
        }

        const s3Response = await s3.getObject({
            Bucket: bucketName,
            Key: fileName
        });

        const fileContent = await s3Response.Body.transformToString();
        const records = await parseCSV(fileContent);

        console.log(`Processing ${records.length} records in parallel...`);
        const results = await Promise.all(records.map(row => processCSVRow(row)));

        const summary = {
            total: results.length,
            updated: results.filter(r => r.status === 'updated').length,
            created: results.filter(r => r.status === 'created').length,
            skipped: results.filter(r => r.status === 'skipped').length,
            errors: results.filter(r => r.status === 'error').length
        };
        console.log('Processing Summary:', summary);

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'CSV processing completed successfully',
                summary
            })
        };
    } catch (error) {
        console.error('Error processing CSV:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                message: 'Error processing CSV file',
                error: error.message
            })
        };
    }
};