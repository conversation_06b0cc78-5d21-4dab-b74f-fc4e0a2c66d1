const axios = require('axios');

const cognitoDomain = process.env.COGNITO_DOMAIN; // e.g., 'mydomain.auth.us-west-2.amazoncognito.com';
const tokenEndpoint = `https://${cognitoDomain}/oauth2/token`;

const authenticateClient = async (client_id, client_secret, grant_type = 'client_credentials') => {
    const authString = `${client_id}:${client_secret}`;
    const encodedAuthString = Buffer.from(authString).toString('base64');

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${encodedAuthString}`
    };

    const data = `grant_type=${grant_type}&client_id=${client_id}`;

    const response = await axios.post(tokenEndpoint, data, { headers });
    return response.data;
};

exports.handler = async (event) => {
    const body = event.body;
    const bodyParams = new URLSearchParams(body);
    const client_id = bodyParams.get('client_id');
    const client_secret = bodyParams.get('client_secret');
    const grant_type = bodyParams.get('grant_type');

    try {
        const tokenResponse = await authenticateClient(client_id, client_secret, grant_type);
        return {
            statusCode: 200,
            body: JSON.stringify(tokenResponse)
        };
    } catch (error) {
        console.error('Error during authentication:', error.message);
        return {
            statusCode: 400,
            body: JSON.stringify({ error: 'Authentication failed' })
        };
    }
};
