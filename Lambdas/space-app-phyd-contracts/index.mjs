import AWS from 'aws-sdk';
import { parse } from 'csv-parse';
import axios from 'axios';

const s3 = new AWS.S3();

const CONFIG = {
    MONGO: {
        BASE_URL: process.env.MONGO_ATLAS_BASE_URL,
        API_KEY: process.env.MONGO_ATLAS_API_KEY,
        APP: process.env.MONGO_APP,
        DATABASE: process.env.MONGO_DATABASE,
        DATASOURCE: process.env.MONGO_DATASOURCE,
        COLLECTION: 'drivingScore',
    },
};

const REQUIRED_COLUMNS = ['VIN', 'contract_number', 'valid_flag'];

const updateDocument = async (vin, document) => {
    const url = `${CONFIG.MONGO.BASE_URL}/app/${CONFIG.MONGO.APP}/endpoint/data/v1/action/updateOne`;
    try {
        const response = await axios.post(url, {
            collection: CONFIG.MONGO.COLLECTION,
            database: CONFIG.MONGO.DATABASE,
            dataSource: CONFIG.MONGO.DATASOURCE,
            filter: { vin: vin },
            update: { $set: document }
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Request-Headers': '*',
                'api-key': CONFIG.MONGO.API_KEY
            }
        });

        if (response.status !== 200 && response.status !== 201) {
            throw new Error(`MongoDB update failed with status ${response.status}: ${JSON.stringify(response.data)}`);
        }
        return response;
    } catch (error) {
        console.error(`Error updating document for VIN ${vin}:`, error.message);
        throw error;
    }
};

const insertDocument = async (document) => {
    const url = `${CONFIG.MONGO.BASE_URL}/app/${CONFIG.MONGO.APP}/endpoint/data/v1/action/insertOne`;
    try {
        const response = await axios.post(url, {
            collection: CONFIG.MONGO.COLLECTION,
            database: CONFIG.MONGO.DATABASE,
            dataSource: CONFIG.MONGO.DATASOURCE,
            document
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Request-Headers': '*',
                'api-key': CONFIG.MONGO.API_KEY
            }
        });

        if (response.status !== 200 && response.status !== 201) {
            throw new Error(`MongoDB insert failed with status ${response.status}: ${JSON.stringify(response.data)}`);
        }
        return response;
    } catch (error) {
        console.error(`Error inserting document for VIN ${document.vin}:`, error.message);
        throw error;
    }
};

const processRowData = (row) => {
    const vin = row.VIN?.trim();
    const contract_number = row.contract_number?.trim();
    const valid_flag = row.valid_flag?.trim().toLowerCase();

    if (!vin || vin.length !== 17) {
        return { error: 'Invalid VIN: Must be 17 characters long' };
    }
    if (!contract_number) {
        return { error: 'Missing contract_number' };
    }
    if (!valid_flag) {
        return { error: 'Missing valid_flag' };
    }

    if (valid_flag !== 'true' && valid_flag !== 'false') {
        return { error: `Invalid valid_flag value: ${row.valid_flag}. Must be 'true' or 'false'` };
    }

    return {
        vin,
        stliPolicyNumber: contract_number,
        isValid: valid_flag === 'true',
    };
};

const processCSVRow = async (row) => {
    try {
        const processedData = processRowData(row);
        if (processedData.error) {
            console.log(`Skipping row for VIN ${row.VIN || 'unknown'}: ${processedData.error}`);
            return { status: 'skipped', vin: row.VIN, reason: processedData.error };
        }

        const updateResponse = await updateDocument(processedData.vin, processedData);
        if (updateResponse.data.matchedCount === 0) {
            await insertDocument(processedData);
            console.log(`Created new document for VIN ${processedData.vin}`);
            return { status: 'created', vin: processedData.vin };
        }

        console.log(`Updated existing document for VIN ${processedData.vin}`);
        return { status: 'updated', vin: processedData.vin };
    } catch (error) {
        console.error(`Error processing document for VIN ${row.VIN}:`, error);
        return { status: 'error', vin: row.VIN, error: error.message };
    }
};

const validateColumns = (headers) => {
    const columnNames = headers.map(header => header.name);
    const missingColumns = REQUIRED_COLUMNS.filter(col => !columnNames.includes(col));
    if (missingColumns.length > 0) {
        throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
    }
};

const parseCSV = async (fileContent) => {
    const records = [];
    let headers = null;

    const parser = parse({
        columns: (headers) => {
            return headers.map(h => ({ name: h.trim() }));
        },
        skip_empty_lines: true,
        on_record: (record, context) => {
            if (!headers) {
                headers = context.columns;
                validateColumns(headers);
            }
            return record;
        }
    });

    return new Promise((resolve, reject) => {
        parser.on('readable', () => {
            let record;
            while ((record = parser.read()) !== null) {
                records.push(record);
            }
        });

        parser.on('error', (error) => {
            console.error('Error parsing CSV:', error.message);
            reject(error);
        });

        parser.on('end', () => resolve(records));

        const content = Buffer.isBuffer(fileContent) ? fileContent.toString() : fileContent;
        parser.write(content);
        parser.end();
    });
};

export const handler = async (event) => {
    try {
        const bucketName = event.Records[0].s3.bucket.name;
        const fileName = decodeURIComponent(event.Records[0].s3.object.key.replace(/\+/g, ' '));

        if (!fileName.toLowerCase().endsWith('.csv')) {
            throw new Error(`Invalid file format. Only CSV files are supported. Received: ${fileName}`);
        }

        const s3Response = await s3.getObject({
            Bucket: bucketName,
            Key: fileName
        }).promise();

        const fileContent = s3Response.Body;
        const records = await parseCSV(fileContent);

        console.log(`Processing ${records.length} records in parallel...`);
        const results = await Promise.all(records.map(row => processCSVRow(row)));

        const summary = {
            total: results.length,
            updated: results.filter(r => r.status === 'updated').length,
            created: results.filter(r => r.status === 'created').length,
            skipped: results.filter(r => r.status === 'skipped').length,
            errors: results.filter(r => r.status === 'error').length
        };
        console.log('Processing Summary:', summary);

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: `CSV processing completed for ${fileName}`,
                summary
            })
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                message: 'Error processing CSV file',
                error: error.message
            })
        };
    }
};