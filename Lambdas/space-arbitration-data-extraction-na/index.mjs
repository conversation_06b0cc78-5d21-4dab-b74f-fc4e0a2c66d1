import { MongoClient } from 'mongodb';
import { createObjectCsvWriter } from 'csv-writer';
import { promises as fsPromises } from 'fs';
import fs from 'fs/promises';
import { createReadStream } from 'fs';
import { S3Client, PutObjectCommand, HeadObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import axios from 'axios';
import path from 'path';

const CONFIG = {
  MONGO: {
    BASE_URL: process.env.MONGO_ATLAS_BASE_URL,
    API_KEY: process.env.MONGO_ATLAS_API_KEY,
    APP: process.env.MONGO_APP,
    DATABASE: process.env.MONGO_DATABASE,
    DATASOURCE: process.env.MONGO_DATASOURCE,
    COLLECTION: 'consentArbitration',
  },
  S3: {
    REGION: 'us-east-1',
    BUCKET: process.env.BUCKET_NAME,
    BASE_PATH: `inbound-to-GADP/${process.env.ENVIRONMENT}/arbitration`,
  },
};

const s3 = new S3Client({
  region: CONFIG.S3.REGION,
  credentials: {
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY,
  },
  maxAttempts: 3,
});

const formatDate = () => {
  const date = new Date();
  return date.toISOString().slice(0, 10).replace(/-/g, '');
};

const fetchMongoData = async () => {
  const maxRetries = 3;
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const apiUrl = `${CONFIG.MONGO.BASE_URL}/app/${CONFIG.MONGO.APP}/endpoint/data/v1/action/find`;
      const query = {
        collection: CONFIG.MONGO.COLLECTION,
        database: CONFIG.MONGO.DATABASE,
        dataSource: CONFIG.MONGO.DATASOURCE,
        filter: { region: "NA", exported: { $ne: true } },
        projection: {
          userId: 1,
          vin: 1,
          consent: 1,
          country: 1,
          source: 1,
          createdAt: 1,
          _id: 1
        },
        sort: { createdAt: 1 },
      };

      const result = await axios.post(apiUrl, query, {
        headers: {
          'Content-Type': 'application/json',
          'api-key': CONFIG.MONGO.API_KEY,
        },
        timeout: 5000,
      });

      return result.data.documents || [];
    } catch (error) {
      lastError = error;
      console.warn(`MongoDB fetch attempt ${attempt} failed: ${error.message}`);

      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  throw new Error(`Failed to fetch MongoDB data after ${maxRetries} attempts: ${lastError.message}`);
};

async function updateExportedFieldInMongoDB(documentIds, batchSize = 30) {
  const tasks = [];
  const apiUrl = `${CONFIG.MONGO.BASE_URL}/app/${CONFIG.MONGO.APP}/endpoint/data/v1/action/updateMany`;

  for (let i = 0; i < documentIds.length; i += batchSize) {
    const batch = documentIds.slice(i, i + batchSize);
    const objectIds = batch.map(id => ({ $oid: id }));

    const updateQuery = {
      collection: CONFIG.MONGO.COLLECTION,
      database: CONFIG.MONGO.DATABASE,
      dataSource: CONFIG.MONGO.DATASOURCE,
      filter: { _id: { $in: objectIds } },
      update: { $set: { exported: true } },
    };

    tasks.push(
      (async () => {
        try {
          const startTime = Date.now();
          const result = await axios.post(apiUrl, updateQuery, {
            headers: {
              'Content-Type': 'application/json',
              'api-key': CONFIG.MONGO.API_KEY,
            }
          });
          const duration = Date.now() - startTime;
          console.log(`Batch updated: ${result.data.modifiedCount} documents in ${duration}ms.`);
        } catch (error) {
          console.error(`Error updating batch: ${error.message}`);
        }
      })()
    );
  }

  // Wait for all tasks to complete
  await Promise.all(tasks);
  console.log('All batches processed.');
}

const createCsvFile = async (data, uploadToS3) => {
  const keys = ['userId', 'vin', 'consent', 'country', 'source', 'createdAt'];

  const formatDate = (epochTime) => {
    const date = new Date(epochTime * 1000);
    return date.toISOString().split('T')[0].replace(/-/g, '');
  };

  const groupedData = data.reduce((groups, item) => {
    const date = formatDate(item.createdAt);
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(item);
    return groups;
  }, {});

  const promises = Object.keys(groupedData).map(async (date) => {
    const dateData = groupedData[date];
    const csvContent = await generateCsvContent(dateData, keys);
    const documentIds = data.map(item => item._id);
    await uploadToS3(csvContent, `arbitration-${date}.csv`, documentIds);
  });

  await Promise.all(promises);
};

const generateCsvContent = (data, keys) => {
  const csvHeader = keys.map(field => (field === 'consent' ? 'arbitration_accepted' : field)).join(',') + '\n';
  const csvRows = data.map(item =>
    keys.map(field => item[field] !== undefined && item[field] !== null ? item[field] : '').join(',')
  ).join('\n');

  return csvHeader + csvRows;
};


const fileExistsInS3 = async (fileName) => {
  const params = {
    Bucket: CONFIG.S3.BUCKET,
    Key: `${CONFIG.S3.BASE_PATH}/${fileName}`,
  };

  try {
    const command = new HeadObjectCommand(params);
    await s3.send(command);
    return true;
  } catch (error) {
    if (error.name === 'NotFound') {
      return false;
    }
    throw error;
  }
};

const streamToString = (stream) => {
  return new Promise((resolve, reject) => {
    let data = '';
    stream.on('data', chunk => {
      data += chunk;
    });
    stream.on('end', () => {
      resolve(data);
    });
    stream.on('error', reject);
  });
};

const uploadToS3 = async (csvContent, fileName, documentIds) => {
  try {
    const fileExist = await fileExistsInS3(fileName);

    if (fileExist) {
      const params = {
        Bucket: CONFIG.S3.BUCKET,
        Key: `${CONFIG.S3.BASE_PATH}/${fileName}`,
      };
      const command = new GetObjectCommand(params);
      const data = await s3.send(command);
      const existingContent = await streamToString(data.Body);

      const lastLine = existingContent.split('\n').pop();
      const rowsToAppend = lastLine && lastLine.trim() !== ''
        ? '\n' + csvContent.split('\n').slice(1).join('\n')
        : csvContent.split('\n').slice(1).join('\n');

      const updatedContent = existingContent + rowsToAppend;

      const putParams = {
        Bucket: CONFIG.S3.BUCKET,
        Key: `${CONFIG.S3.BASE_PATH}/${fileName}`,
        Body: updatedContent,
        ContentType: 'text/csv',
      };
      const putCommand = new PutObjectCommand(putParams);
      await s3.send(putCommand);

      console.log(`Appended data to file: ${fileName}`);
    } else {
      const putParams = {
        Bucket: CONFIG.S3.BUCKET,
        Key: `${CONFIG.S3.BASE_PATH}/${fileName}`,
        Body: csvContent,
        ContentType: 'text/csv',
      };
      const putCommand = new PutObjectCommand(putParams);
      await s3.send(putCommand);

      console.log(`New file created: ${fileName}`);
    }
    await updateExportedFieldInMongoDB(documentIds);
  } catch (error) {
    console.error(`Error handling file ${fileName} in S3:`, error);
  }
};

export const handler = async (event) => {
  const startTime = Date.now();
  let response;

  try {
    const data = await fetchMongoData();
    console.info('Data fetched from MongoDB', { recordCount: data.length });

    await createCsvFile(data, uploadToS3);

    response = {
      statusCode: 200,
      body: {
        message: data.length > 0 ? 'File uploaded successfully' : 'Empty file uploaded successfully',
        recordCount: data.length,
        processingTime: Date.now() - startTime
      },
    };

  } catch (error) {
    console.error('Error in processing', {
      error: error.message,
      stack: error.stack,
      processingTime: Date.now() - startTime
    });

    response = {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Error processing data',
        error: error.message,
        processingTime: Date.now() - startTime
      }),
    };
  }

  return response;
};