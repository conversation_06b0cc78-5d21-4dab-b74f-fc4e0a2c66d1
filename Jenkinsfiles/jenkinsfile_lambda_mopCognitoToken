properties([
    parameters([
        choice(
            choices: ['develop', 'integ', 'preprod', 'prod'],
            description: 'Choix de l environnement cible',
            name: 'targetEnvironment'
        )
    ])
])

node() {
    def projectName = env.BRANCH_NAME.toLowerCase().replaceAll(/[^a-z0-9]/, "");
    String currentDate = new java.text.SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    def targetEnvironment = env.targetEnvironment;
    def lambdaName = 'lambda-mopCognitoToken';

    printf('current branch %1s', [env.BRANCH_NAME]);
    printf('current target environment %1s', [targetEnvironment]);

    timestamps {
        try {
            // BUILD
            build(currentDate, projectName, targetEnvironment)
            // DEPLOY
            stageDeploy(currentDate, targetEnvironment, lambdaName)
        } catch(e) {
            throw e;
        } finally {
            try {
                // Clean-up actions if necessary
            } catch(ex) {
                println(ex.toString());
            }
        }
    }
}

///////////////////////////////////
//  - GIT PULL
//  - Install dependencies
//  - PUSH
//  - DEPLOY
///////////////////////////////////

def build(String currentDate, String projectName, String targetEnvironment) {
    stageCheckout()
}

////////////////////////////////////
// GIT PULL FOR JENKINS
///////////////////////////////////
def stageCheckout() {
    stage('Checkout') {
        deleteDir()
        checkout scm
    }
}

////////////////////////////////////
// DEPLOY
///////////////////////////////////
def stageDeploy(String currentDate, String targetEnvironment, String lambdaName) {
    stage('Deploy') {
        // Change directory to "Lambdas" before running the deploy script
        dir('Lambdas') {
            sh "bash deploy.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${lambdaName}"
        }
    }
}
