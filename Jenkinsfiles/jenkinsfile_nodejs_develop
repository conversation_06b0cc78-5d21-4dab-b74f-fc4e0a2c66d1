pipeline {
    agent any

    environment {
        AWS_REGION = 'eu-west-1' // e.g., us-east-1
        ECR_REPO = 'space' // Replace with your ECR repo name
        ECR_URI = "186546832638.dkr.ecr.eu-west-1.amazonaws.com/${ECR_REPO}"
    }

    stages {
        stage('Prepare Workspace') {
            steps {
                script {
                    echo "Cleaning workspace..."
                    deleteDir() // Deletes all files in the current workspace
                    checkout scm
                    // Set currentDate within the script block
                    currentDate = new Date().format('yyyy-MM-dd_HH-mm-ss')
                }
            }
        }

        stage('Clone Repositories') {
            steps {
                script {
                    // Clone the code repo (already configured in Jenkins job)
                    echo "Cloning deployment repo..."
                    sh "rm -rf ./.git"
                    sh "<NAME_EMAIL>:D4UDigitalPlatform/space-aws-infra.git /tmp/${currentDate}"
                    sh "cp -r  /tmp/${currentDate}/config/_init ./process/space-proc-data-api"
                    sh "cp -r /tmp/${currentDate}/deployment/scripts ./process/space-proc-data-api"
                    sh "rm -rf /tmp/${currentDate}"
                    sh "bash ./process/space-proc-data-api/scripts/container.sh ${currentDate}"
                    sh "bash ./process/space-proc-data-api/scripts/aws_login_docker.sh"
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                script {
                    echo "Building Docker image..."
                    currentDate = new Date().format('yyyy-MM-dd_HH-mm-ss')
                    sh '''
                    docker build -f ./process/space-proc-data-api/_init/docker/aws/nodejs_app_image/Dockerfile \
                    -t ${ECR_URI}:spaceProcDataApi_develop_develop_${currentDate} \
                    ./process/space-proc-data-api
                    '''
                }
            }
        }

        stage('Push to ECR') {
            steps {
                script {
                    echo "Pushing Docker image to ECR..."
                    sh '''
                    aws ecr get-login-password --region ${AWS_REGION} | \
                    docker login --username AWS --password-stdin ${ECR_URI}
                    docker push ${ECR_URI}:spaceProcDataApi_develop_develop_${currentDate}
                    '''
                }
            }
        }

        stage('Deploy to ECS') {
            steps {
                script {
                    echo "Deploying to ECS Fargate..."
                    sh """
                    aws ecs update-service --cluster space-cluster-develop \
                    --service spaceProcDataApi-develop \
                    --region ${AWS_REGION} \
                    --force-new-deployment
                    """
                }
            }
        }
    }

    post {
        always {
            echo "Pipeline completed."
        }
    }
}
