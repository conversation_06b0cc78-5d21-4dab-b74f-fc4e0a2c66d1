properties([parameters([choice(choices: ['space-exp-shop','space-exp-me' , 'space-proc-user-veh', 'space-proc-me', 'space-proc-shop', 'space-proc-settings', 'space-proc-car', 'space-proc-notification', 'space-proc-charging-station', 'space-proc-data-api','space-sys-idp', 'space-sys-mop', 'space-sys-omni', 'space-sys-sdpr', 'space-sys-apdv', 'space-sys-firebase', 'space-sys-service-advisor', 'space-sys-f2m-locator', 'space-sys-sams-data', 'space-sys-f2mc', 'space-sys-gadp', 'space-sys-user-db'], description: 'Choix de l application cible', name: 'appName')])])


node () {
    def projectName = env.BRANCH_NAME.toLowerCase().replaceAll(/[^a-z0-9]/, "");
    def directory = pwd();
    String currentDate = new java.text.SimpleDateFormat("yyyyMMddHHmmssMs").format(new Date());

    def (value_1, value_2, job) = env.JOB_NAME.tokenize( '/' );
    def (prd, app_name, target_environment) = job.tokenize( '_' );

    def configs = ['space-exp-me': ['ms_name': 'spaceExpMe', 'path': './experience/space-exp-me']
    ,'space-exp-shop': ['ms_name': 'spaceExpShop', 'path': './experience/space-exp-shop']
    ,'space-proc-user-veh': ['ms_name': 'spaceProcUserVeh', 'path': './process/space-proc-user-veh']
    ,'space-proc-shop': ['ms_name': 'spaceProcShop', 'path': './process/space-proc-shop']
    ,'space-proc-me': ['ms_name': 'spaceProcMe', 'path': './process/space-proc-me']
    ,'space-proc-settings': ['ms_name': 'spaceProcSettings', 'path': './process/space-proc-settings']
    ,'space-proc-car': ['ms_name': 'spaceProcCar', 'path': './process/space-proc-car']
    ,'space-proc-notification': ['ms_name': 'spaceProcNotification', 'path': './process/space-proc-notification']
    ,'space-proc-charging-station': ['ms_name': 'spaceProcChrgStation', 'path': './process/space-proc-charging-station']
    ,'space-proc-data-api': ['ms_name': 'spaceProcDataApi', 'path': './process/space-proc-data-api']
    ,'space-sys-idp': ['ms_name': 'spaceSysIdp', 'path': './system/space-sys-idp']
    ,'space-sys-omni': ['ms_name': 'spaceSysOmni', 'path': './system/space-sys-omni']
    ,'space-sys-mop': ['ms_name': 'spaceSysMop', 'path': './system/space-sys-mop']
    ,'space-sys-sdpr': ['ms_name': 'spaceSysSdpr', 'path': './system/space-sys-sdpr']
    ,'space-sys-apdv': ['ms_name': 'spaceSysApdv', 'path': './system/space-sys-apdv']
    ,'space-sys-firebase': ['ms_name': 'spaceSysFirebase', 'path': './system/space-sys-firebase']
    ,'space-sys-service-advisor': ['ms_name': 'spaceSysServiceAdv', 'path': './system/space-sys-service-advisor']
    ,'space-sys-f2m-locator': ['ms_name': 'spaceSysF2mLocator', 'path': './system/space-sys-f2m-locator']
    ,'space-sys-sams-data': ['ms_name': 'spaceSysSamsData', 'path': './system/space-sys-sams-data']
    ,'space-sys-f2mc': ['ms_name': 'spaceSysF2mc', 'path': './system/space-sys-f2mc']
    ,'space-sys-gadp': ['ms_name': 'spaceSysGadp', 'path': './system/space-sys-gadp']
    ,'space-sys-user-db': ['ms_name': 'spaceSysUserDb', 'path': './system/space-sys-user-db']]



    def appName = configs[params.appName]['ms_name'];
    def path = configs[params.appName]['path'];
    def targetEnvironment = target_environment.toLowerCase();


    printf('current branch %1s', [env.BRANCH_NAME]);
    printf('current target environment %1s', [targetEnvironment]);
    printf('current application  name %1s', [appName]);

    timestamps {
    try {
            // Checkout
            stageCheckout()

            //load common jenkinsfile
            def rootDir = pwd()
            def functions = load "${rootDir}/Jenkinsfiles/Jenkinsfile_common"

            //Install
            functions.stageInstall(currentDate, path, targetEnvironment, appName)

            // Push Image
            functions.stagePushImage(currentDate, targetEnvironment, appName, path)

            // DEPLOY
            functions.stageDeploy(currentDate, targetEnvironment, appName, path)


        } catch(e) {
            throw e;
        } finally {
            try{
                //check
            } catch(ex) {
                println(ex.toString());
            }
        }
    }
}


////////////////////////////////////
// GIT PULL FOR JENKINS
///////////////////////////////////
def stageCheckout() {
    stage('Checkout') {
        deleteDir()
        checkout scm
    }
}
