# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
imports:
    - { resource: 'refresh_queues.yaml' }
parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Service\ProcUserVehicleConnector:
        arguments:
            $url: "%env(MS_PROC_USER_VEH_URL)%"
    App\EventListener\ExceptionListener:
        tags: [kernel.event_listener]

    Aws\Sqs\SqsClient:
        class: Aws\Sqs\SqsClient
        arguments:
            - 
                version: 'latest'
                region: '%env(AWS_REGION)%'
                credentials:
                    key: '%env(AWS_ACCESS_KEY)%'
                    secret: '%env(AWS_SECRET_KEY)%'
    
    App\Service\AwsSqsService:
        arguments:
            $queueBaseUrl: '%env(AWS_SQS_QUEUE_BASE_URL)%'
    
    App\Service\RefreshQueueService:
        arguments:
            $queuesConfig: '%refresh_queues%'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
