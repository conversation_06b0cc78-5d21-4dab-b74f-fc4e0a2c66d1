<?php
namespace App\EventListener;

use App\Helper\ErrorResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;

class ExceptionListener
{
    public function __invoke(ExceptionEvent $event): void
    {
        // You get the exception object from the received event
        $exception = $event->getThrowable();

        // Customize your response object to display the exception details
        $response = new ErrorResponse($exception->getMessage(), $exception->getCode());

        // HttpExceptionInterface is a special type of exception that
        // holds status code and header details
        if ($exception instanceof HttpExceptionInterface) {
            $response->setCode($exception->getStatusCode());
        } 

        // sends the modified response object to the event
        $event->setResponse(new JsonResponse($response->toArray()['content']));
    }
}