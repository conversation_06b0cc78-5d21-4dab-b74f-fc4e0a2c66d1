<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Trait\LoggerTrait;
use App\Helper\SuccessResponse;
use App\Service\VehicleService;
use Exception;
use Symfony\Component\HttpFoundation\Response;


class VehicleManager
{
    use LoggerTrait;
    /**
     * @var VehicleService
     */
    private $service;

    function __construct(VehicleService $service)
    {
        $this->service = $service;
    }

   

    public function getVehicles(string $userId)
    {
        try {
            $this->logger->info("VehicleManager::getVehicles for userId " . $userId);
            $response = $this->service->getVehicles($userId);
            if (Response::HTTP_OK == $response->getCode()) {
                return new SuccessResponse($response->getData()["success"]);
            }
            $result = $response->getData();
            if (isset($result["error"]["message"])) {
                $result = $result["error"]["message"];
            }
            return new ErrorResponse($result, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("VehicleManager::getVehicles : Cached Exception " . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get Vehicles Summary
     * @param string $userId
     * @param string $vehicleId
     * @return ResponseArrayFormat
     */
    public function getVehicleSummary(string $userId, string $vehicleId): ResponseArrayFormat
    {
        try {
            $response = $this->service->getVehicleSummary($userId, $vehicleId);
            if ($response->getCode() == Response::HTTP_OK) {
                return new SuccessResponse($response->getData()['success']);
            }
            $result = $response->getData();
            if (isset($result["error"]["message"])) {
                $result = $result["error"]["message"];
            }
            return new ErrorResponse($result, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("Catched Exception VehicleManager::getVehicleSummary " . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
    /**
     * Add Vehicles Data
     * @param string $brand
     * @param string $country
     * @param string $language
     * @param string $source
     * @param string $vin
     * @return ResponseArrayFormat
     */

    public function addVehicle(string $brand, string $country, string $language, string $source, string $vin, string $mileage)
      {
        try {
            $response = $this->service->addVehicle($brand,$country,$language,$source,$vin,$mileage);
            if ($response->getCode() == Response::HTTP_OK) {
                return new SuccessResponse($response->getData()['success']);
            }
            $result = $response->getData();
            if (isset($result["error"]["message"])) {
                $result = $result["error"]["message"];
            }
            $errorMessage = $response->getData()['error']['message'] ?? 'Error result';             
            $this->logger->error("Error response VehicleManager::addVehicle " . $errorMessage);   
            return new ErrorResponse($result, $response->getCode());
        } catch (\Exception $e) {            
            $this->logger->error("Catched Exception VehicleManager::addVehicle " . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());

        }

    }
}


