<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Error response class
 */
class ErrorResponse implements ResponseArrayFormat
{
    private $code;

    private $message;

    private $errors;

    public function __construct(mixed $errors, ?int $code = Response::HTTP_BAD_REQUEST)
    {
        $this->code = $code;
        $this->message = is_string($errors) ? $errors : json_encode($errors);
    }

    public function setCode($code)
    {
        $this->code = $code;
        return $this;
    }

    public function setMessage($message)
    {
        $this->message = $message;
        return $this;
    }

    public function setErrors($errors)
    {
        $this->errors = $errors;
        return $this;
    }

    public function toArray(): array
    {
        $response = [];
        if ($this->message) {
            $response['message'] = $this->message;
        }
        if ($this->errors) {
            $response['errors'] = $this->errors;
        }
        return [
            'code'    => $this->code < 400 ? Response::HTTP_BAD_REQUEST : $this->code,
            'content' => ["error" => $response]
        ];
    }
}