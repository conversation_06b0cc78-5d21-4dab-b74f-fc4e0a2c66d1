<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Manage rest responses
 */
class WSResponse
{
    private $code = Response::HTTP_OK;
    private $data;

    public function __construct(int $code, mixed $data)
    {
        $this->code = $code;
        $this->data = $data;
    }
    public function setCode($code)
    {
        $this->code = $code;
        return $this;
    }

    public function getCode()
    {
        return $this->code;
    }

    public function setData($data)
    {
        $this->data = $data;
        return $this;
    }

    public function getData()
    {
        return $this->data;
    }
}