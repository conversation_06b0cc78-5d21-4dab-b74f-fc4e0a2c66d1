<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Success response class
 */
class SuccessResponse implements ResponseArrayFormat
{
    private $code;
    private $data;

    public function __construct(mixed $data, ?int $code = Response::HTTP_OK)
    {
        $this->code = $code;
        $this->data = $data;
    }

    public function setCode($code)
    {
        $this->code = $code;
        return $this;
    }

    public function getCode()
    {
        return $this->code;
    }

    public function setData($data)
    {
        $this->data = $data;
        return $this;
    }

    public function getData()
    {
        return $this->data;
    }

    public function toArray(): array
    {
        $this->data = is_array($this->data) || is_string($this->data) ? $this->data : json_decode($this->data, true);

        return [
            'content' => ["success" => $this->data],
            'code'    => $this->code
        ];
    }
}