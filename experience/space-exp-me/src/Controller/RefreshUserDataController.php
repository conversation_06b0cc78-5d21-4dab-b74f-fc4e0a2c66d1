<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use App\Manager\RefreshDataManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Attributes as OA;
use App\Trait\ValidationResponseTrait;
use App\Model\ObjectsListToRefreshModel;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Nelmio\ApiDocBundle\Annotation\Model;

#[Route("/v1")]
class RefreshUserDataController extends AbstractController
{
    use ValidationResponseTrait;

    #[
        Route(
            "/refresh-data",
            name: "refresh_data",
            methods: ["POST"],
            format: "json"
        )
    ]
    #[
        OA\Parameter(
            name: "userId",
            in: "header",
            required: true,
            description: "Gigya user id",
            schema: new OA\Schema(type: "string")
        )
    ]
    #[
        OA\RequestBody(
            content: new OA\JsonContent(
                example: <<<JSON
                {
                    "objects": {
                        "subscription": {
                            "vin": "12345"
                        },
                        "vehicle": true
                    },
                    "source": "WEB"
                }
                JSON
                ,
                ref: new Model(type: ObjectsListToRefreshModel::class)
            )
        )
    ]
    #[
        OA\Response(
            response: 200,
            description: "Success Response",
            content: new OA\JsonContent(
                type: "object",
                properties: [new OA\Property(property: "success")]
            )
        )
    ]
    #[
        OA\Response(
            response: 400,
            description: "Bad Request example :Failed to refresh objects object_to_refresh)",
            content: new OA\JsonContent(
                type: "object",
                properties: [
                    new OA\Property(
                        property: "error",
                        properties: [
                            new OA\Property(property: "message")
                        ]
                    ),
                ]
            )
        )
    ]
    #[
        OA\Response(
            response: 422,
            description: "Bad Request",
            content: new OA\JsonContent(
                type: "object",
                properties: [
                    new OA\Property(
                        property: "error",
                        properties: [
                            new OA\Property(property: "message"),
                            new OA\Property(property: "errors", properties: []),
                        ]
                    ),
                ]
            )
        )
    ]
    public function refreshData(
        Request $request,
        RefreshDataManager $refreshDataManager,
        ValidatorInterface $validator
    ) {
        $userId = $request->headers->get('userId');
        $dto = json_decode($request->getContent(), true);
        $objectsListDto = new ObjectsListToRefreshModel(
            $dto["objects"] ?? [],
            $dto["source"] ?? '',
            $userId ?? ''
        );

        $errors = $validator->validate($objectsListDto);
        $messages = $this->getValidationMessages($errors);

        if ($messages !== []) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $refreshDataManager
            ->enqueueObjectsToRefresh($objectsListDto)
            ->toArray();

        return $this->json($response['content'], $response['code']);
    }
}
