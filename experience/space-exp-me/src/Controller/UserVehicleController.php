<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use App\Manager\VehicleManager;
use App\Model\VehicleModel;
use App\Trait\ValidationResponseTrait;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use PHPUnit\Util\Json;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Event\ViewEvent;

#[Route('v1/vehicles', name: 'user_vehicle_')]
class UserVehicleController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: '_vehicles', methods: ['GET'])]

    #[OA\Response(
        response: 200,
        description: 'Success Response',
        content: new JsonContent(type: 'object', properties:[
            new OA\Property(property: 'success', properties:[
                new OA\Property(property: 'vehicles', ref: new Model(type: VehicleModel::class))
            ])
            ])
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(type: 'object', properties:[
            new OA\Property(property: 'error', properties:[
                new OA\Property(property:'message'),
                new OA\Property(property:'errors',properties: []),
            ]),
            ]
        )
    )]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'User Vehicles')]
    public function getVehicles(Request $request, VehicleManager $vehicleManager, ValidatorInterface $validator): JsonResponse
    {
        $userId = $request->headers->get('userId');
        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId'     => new Assert\NotBlank
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }
        $response = $vehicleManager->getVehicles($userId)->toArray();
        return $this->json($response['content'], $response['code']);
    }
    
    #[Route('/{id}/summary', name: 'summary', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Success Response',
        content: new JsonContent(type: 'object', properties:[
            new OA\Property(property: 'success', properties:[
                new OA\Property(property: 'url')
            ])
        ])
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(type: 'object', properties:[
            new OA\Property(property: 'error', properties:[
                new OA\Property(property:'message'),
                new OA\Property(property:'errors',properties: []),
            ]),
            ]
        )
    )]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'Vehicles')]
    
    public function VehicleOrderSummary(ValidatorInterface $validator, VehicleManager $vehicleManager, Request $request, string $id): JsonResponse
    {    
        $userId = $request->headers->get('userId');
        $errors = $validator->validate(
            compact('id', 'userId'),
            new Assert\Collection([
                    'id'     => new Assert\NotBlank(),
                    'userId' => new Assert\NotBlank(),
                ])
        );
        $messages = $this->getValidationMessages($errors);        
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }
        $response = $vehicleManager->getVehicleSummary($userId, $id)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/add', name: 'addVehicle', methods: ['POST'])]
    #[OA\Response(
        response: 200,
        description: 'Success Response',
        content: new JsonContent(type: 'object', properties:[
            new OA\Property(property: 'success', properties:[
                new OA\Property(property: 'url')
            ])
        ])
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(type: 'object', properties:[
            new OA\Property(property: 'error', properties:[
                new OA\Property(property:'message'),
                new OA\Property(property:'errors',properties: []),
            ]),
            ]
        )
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(

        name: 'source',
        in: 'query',
        description: 'Source',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'vin',
                    description: 'VIN code',
                    type: 'string',
                    example: 'VR3UPHNKSKT101600'
                ),
                new OA\Property(
                    property: 'mileage',
                    description: 'Mileage value for the vehicle; it is a string, but must be rapresent a positive integer; zero also is a valid value',
                    type: 'string',
                    example: '0'
                ),
            ]
        )

    )]

    #[OA\Tag(name: 'Vehicles')]
    public function addVehicle(ValidatorInterface $validator, VehicleManager $vehicleManager, Request $request): JsonResponse
    {
        $brand = $request->query->get('brand');
        $country = $request->query->get('country');
        $language = $request->query->get('language');
        $source = $request->query->get('source');
        //retrieve vin and mileage from the request body         
        $content = json_decode($request->getContent(), true);
        $vin = $content['vin'] ?? '';
        $mileage = $content['mileage'] ?? '';

        $requestData = json_decode($request->getContent(), true);
        $errors = $validator->validate(
            compact('brand', 'country', 'language', 'source', 'vin', 'mileage'),
            new Assert\Collection([
                'brand'     => new Assert\NotBlank,
                'country'   => new Assert\NotBlank,
                'language'  => new Assert\NotBlank,
                'source'     => new Assert\NotBlank,
                'vin'       => new Assert\NotBlank,
                'mileage'   => new Assert\GreaterThanOrEqual(0),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }
        $response = $vehicleManager->addVehicle($brand,$country,$language,$source,$vin,$mileage)->toArray();
        return $this->json($response['content'], $response['code']);
    }    
}