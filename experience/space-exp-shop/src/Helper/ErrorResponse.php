<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Error response class.
 */
class ErrorResponse implements ResponseArrayFormat
{

    private $message;

    public function __construct(private mixed $errors, private ?int $code = Response::HTTP_BAD_REQUEST)
    {
        $this->code = $code;
        if (is_string($errors)) {
            $this->message = $errors;
        } else {
            $this->message = json_encode($errors);
        }
    }

    public function setCode($code)
    {
        $this->code = $code;

        return $this;
    }

    public function setMessage($message)
    {
        $this->message = $message;

        return $this;
    }

    public function setErrors($errors)
    {
        $this->errors = $errors;

        return $this;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function getMessage(): string
    {
        return $this->message ?? '';
    }

    public function getErrors(): array
    {
        return $this->errors ?? [];
    }

    public function toArray(): array
    {
        $response = [];
        if ($this->message) {
            $response['message'] = $this->message;
        }
        if ($this->errors) {
            $response['errors'] = $this->errors;
        }

        return [
            'code' => $this->code < Response::HTTP_BAD_REQUEST ? Response::HTTP_BAD_REQUEST : $this->code,
            'content' => ['error' => $response],
        ];
    }
}
